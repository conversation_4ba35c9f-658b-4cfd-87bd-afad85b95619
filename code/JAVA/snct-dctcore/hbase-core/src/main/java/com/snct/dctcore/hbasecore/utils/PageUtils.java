package com.snct.dctcore.hbasecore.utils;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: PageUtils
 * @Description: HBase分页工具类
 * @author: wze<PERSON>
 * @date: 2025-08-25 16:51
 */
@Component
public class PageUtils {
    /**
     * 获取分页RowKey列表
     * 根据完整RowKey列表和分页参数，获取当前页的RowKey列表
     *
     * @param rowKeyList  完整的RowKey列表（无序）
     * @param currentPage 当前页码（从1开始）
     * @param pageSize    每页数据量
     * @param sortOrder   排序方式
     * @return 包含以下信息的Map：
     *         - rowKeyList: 当前页的RowKey列表（List<String>）
     *         - total: 总记录数（Integer）
     *         - currentPage: 当前页码（Integer）
     *         - pageSize: 每页大小（Integer）
     *         - sortOrder: 排序方式（String）
     */
    public Map<String, Object> getPageRowKeys(List<String> rowKeyList, int currentPage, int pageSize, String sortOrder) {
        Map<String, Object> pageInfo = new HashMap<>();
        
        // 设置分页基本信息
        pageInfo.put("currentPage", currentPage);
        pageInfo.put("pageSize", pageSize);
        pageInfo.put("sortOrder", sortOrder);

        // 处理空列表情况
        if (rowKeyList == null || rowKeyList.isEmpty()) {
            pageInfo.put("total", 0);
            pageInfo.put("rowKeyList", new ArrayList<>());
            return pageInfo;
        }

        // 记录总数
        pageInfo.put("total", rowKeyList.size());
        
        // 计算起始索引
        int startIndex = (currentPage - 1) * pageSize;
        if (startIndex >= rowKeyList.size()) {
            // 如果起始索引超出范围，重置为第一页
            startIndex = 0;
            pageInfo.put("currentPage", 1);
        }

        // 计算结束索引（不包含）
        int endIndex = Math.min(startIndex + pageSize, rowKeyList.size());

        // 获取当前页的RowKey列表
        List<String> currentPageRowKeys = rowKeyList.subList(startIndex, endIndex);

        // 直接返回RowKey列表
        pageInfo.put("rowKeyList", currentPageRowKeys);

        return pageInfo;
    }

    /**
     * 计算分页边界RowKey
     * @deprecated 该方法返回完整的RowKey列表
     *
     * @param rowKeyList  完整的RowKey列表
     * @param currentPage 当前页码（从1开始）
     * @param pageSize    每页数据量
     * @param sortOrder   排序方式
     * @return 包含以下信息的Map：
     *         - sRowKey: 起始RowKey，对应当前页第一条数据
     *         - eRowKey: 结束RowKey，对应当前页最后一条数据
     *         - total: 总记录数
     *         - currentPage: 当前页码
     *         - pageSize: 每页大小
     *         - sortOrder: 排序方式
     */
    @Deprecated
    public Map<String, String> getSERowKey(List<String> rowKeyList, int currentPage, int pageSize, String sortOrder) {
        Map<String, String> pageInfo = new HashMap<>();

        // 设置分页基本信息
        pageInfo.put("currentPage", String.valueOf(currentPage));
        pageInfo.put("pageSize", String.valueOf(pageSize));
        pageInfo.put("sortOrder", sortOrder);

        // 处理空列表情况
        if (rowKeyList == null || rowKeyList.isEmpty()) {
            pageInfo.put("total", "0");
            pageInfo.put("sRowKey", "");
            pageInfo.put("eRowKey", "");
            return pageInfo;
        }

        // 记录总数
        pageInfo.put("total", String.valueOf(rowKeyList.size()));

        // 计算起始索引
        int startIndex = (currentPage - 1) * pageSize;
        if (startIndex >= rowKeyList.size()) {
            // 如果起始索引超出范围，重置为第一页
            startIndex = 0;
        }

        // 计算结束索引
        int endIndex = startIndex + pageSize - 1;

        // 设置起始RowKey
        pageInfo.put("sRowKey", rowKeyList.get(startIndex));

        // 设置结束RowKey
        if (endIndex >= rowKeyList.size()) {
            String lastRowKey = rowKeyList.get(rowKeyList.size() - 1);
            pageInfo.put("eRowKey", lastRowKey);
        } else {
            pageInfo.put("eRowKey", rowKeyList.get(endIndex));
        }

        return pageInfo;
    }
}
